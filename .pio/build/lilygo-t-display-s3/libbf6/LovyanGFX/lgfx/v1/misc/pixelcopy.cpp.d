.pio/build/lilygo-t-display-s3/libbf6/LovyanGFX/lgfx/v1/misc/pixelcopy.cpp.o: \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/misc/pixelcopy.cpp \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/misc/pixelcopy.hpp \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/misc/colortype.hpp \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/misc/../../utility/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/misc/enum.hpp
