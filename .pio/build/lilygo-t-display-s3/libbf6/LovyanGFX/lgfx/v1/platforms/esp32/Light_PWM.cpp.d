.pio/build/lilygo-t-display-s3/libbf6/LovyanGFX/lgfx/v1/platforms/esp32/Light_PWM.cpp.o: \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/platforms/esp32/Light_PWM.cpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/qio_opi/include/sdkconfig.h \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/platforms/esp32/Light_PWM.hpp \
 .pio/libdeps/lilygo-t-display-s3/LovyanGFX/src/lgfx/v1/platforms/esp32/../../Light.hpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-ledc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp_arduino_version.h
