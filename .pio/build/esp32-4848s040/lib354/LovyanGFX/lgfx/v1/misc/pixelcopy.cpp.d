.pio/build/esp32-4848s040/lib354/LovyanGFX/lgfx/v1/misc/pixelcopy.cpp.o: \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/pixelcopy.cpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/pixelcopy.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/colortype.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/../../utility/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/enum.hpp
