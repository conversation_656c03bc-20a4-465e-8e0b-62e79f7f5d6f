.pio/build/esp32-4848s040/lib354/LovyanGFX/lgfx/v1/platforms/esp32/Light_PWM.cpp.o: \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/Light_PWM.cpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/qio_opi/include/sdkconfig.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/Light_PWM.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../../Light.hpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-ledc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp_arduino_version.h
