; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
default_envs = simple
; default_envs = observer
src_dir = examples/${platformio.default_envs}

; Common settings for all environments
[env]
platform = espressif32
board = lilygo-t-display-s3
framework = arduino
monitor_speed = 115200
lib_deps =
	../ESP32ButtonHandler

; Simple example environment
[env:simple]

; Observer Pattern example environment
[env:observer]
