{"name": "ESP32ButtonHandler", "version": "2.0.0", "description": "A C++ ESP32 library for handling buttons with observer pattern support, allowing multiple observers and lambda callbacks.", "keywords": ["button", "esp32", "input", "observer", "callback"], "repository": {"type": "git", "url": "https://github.com/yawom/ESP32ButtonHandler.git"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/yawom", "maintainer": true}], "license": "BSD-3-<PERSON><PERSON>", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": "espressif32"}