// AUTOMATICALLY GENERATED FILE. PLEASE DO NOT MODIFY IT MANUALLY
//
// PlatformIO Debugging Solution
//
// Documentation: https://docs.platformio.org/en/latest/plus/debugging.html
// Configuration: https://docs.platformio.org/en/latest/projectconf/sections/env/options/debug/index.html

{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug",
            "executable": "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/build/esp32-4848s040/firmware.elf",
            "projectEnvName": "esp32-4848s040",
            "toolchainBinDir": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "preLaunchTask": {
                "type": "PlatformIO",
                "task": "Pre-Debug (esp32-4848s040)"
            }
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (skip Pre-Debug)",
            "executable": "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/build/esp32-4848s040/firmware.elf",
            "projectEnvName": "esp32-4848s040",
            "toolchainBinDir": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/bin",
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (without uploading)",
            "executable": "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/build/esp32-4848s040/firmware.elf",
            "projectEnvName": "esp32-4848s040",
            "toolchainBinDir": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "loadMode": "manual"
        }
    ]
}
