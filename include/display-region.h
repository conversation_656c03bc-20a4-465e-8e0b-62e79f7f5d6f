#ifndef DISPLAY_REGION_H
#define DISPLAY_REGION_H

#include <Arduino.h>
#include <LovyanGFX.hpp>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include "hardware.h"

/**
 * Represents a bounded region of the display that can be safely accessed by a single thread.
 * All drawing operations are automatically clipped to the region boundaries.
 */
class DisplayRegion
{
public:
    /**
     * Constructor
     * @param gfx Reference to the main LGFX instance
     * @param mutex Mutex for thread-safe access to the display
     * @param x Left boundary of the region
     * @param y Top boundary of the region
     * @param width Width of the region
     * @param height Height of the region
     * @param id Unique identifier for this region
     */
    DisplayRegion(LGFX& gfx, SemaphoreHandle_t mutex, int32_t x, int32_t y, int32_t width, int32_t height, uint8_t id);
    
    /**
     * Destructor
     */
    ~DisplayRegion();

    // Basic drawing operations (automatically clipped to region)
    void fillRect(int32_t x, int32_t y, int32_t w, int32_t h, const uint32_t& color);
    void drawRect(int32_t x, int32_t y, int32_t w, int32_t h, const uint32_t& color);
    void fillCircle(int32_t x, int32_t y, int32_t r, const uint32_t& color);
    void drawCircle(int32_t x, int32_t y, int32_t r, const uint32_t& color);
    void drawLine(int32_t x0, int32_t y0, int32_t x1, int32_t y1, const uint32_t& color);
    void drawPixel(int32_t x, int32_t y, const uint32_t& color);
    
    // Text operations
    void setCursor(int32_t x, int32_t y);
    void setTextColor(const uint32_t& color);
    void setTextColor(const uint32_t& fgcolor, const uint32_t& bgcolor);
    void setTextSize(float size);
    void print(const char* text);
    void print(const String& text);
    void print(int value);
    void print(float value);
    void printf(const char* format, ...);
    
    // Region management
    void clear(const uint32_t& color = 0x0000); // Clear the entire region
    void refresh(); // Force a refresh of the region
    
    // Getters
    int32_t getX() const { return _x; }
    int32_t getY() const { return _y; }
    int32_t getWidth() const { return _width; }
    int32_t getHeight() const { return _height; }
    uint8_t getId() const { return _id; }
    
    // Check if coordinates are within this region
    bool contains(int32_t x, int32_t y) const;
    
    // Get region boundaries
    int32_t getLeft() const { return _x; }
    int32_t getRight() const { return _x + _width - 1; }
    int32_t getTop() const { return _y; }
    int32_t getBottom() const { return _y + _height - 1; }

private:
    LGFX& _gfx;
    SemaphoreHandle_t _mutex;
    int32_t _x, _y, _width, _height;
    uint8_t _id;
    
    // Helper methods
    bool acquireLock(TickType_t timeout = portMAX_DELAY);
    void releaseLock();
    bool clipCoordinates(int32_t& x, int32_t& y, int32_t& w, int32_t& h);
    void setClipRegion();
    void restoreClipRegion();
    
    // Store original clip region for restoration
    int32_t _originalClipX, _originalClipY, _originalClipW, _originalClipH;
};

#endif // DISPLAY_REGION_H
