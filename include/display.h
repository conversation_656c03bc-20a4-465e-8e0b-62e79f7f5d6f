#ifndef DISPLAY_H
#define DISPLAY_H

#include "hardware.h"
#include "thread-safe-display.h"

class Display
{
    ThreadSafeDisplay threadSafeDisplay;

public:
    Display() : threadSafeDisplay(LCD_WIDTH, LCD_HEIGHT) {}

    bool setup()
    {
        bool result = threadSafeDisplay.setup();

        if (result)
        {
            createDemoRegions();
        }

        return result;
    }

    /**
     * Create a display region for exclusive use by a thread
     * @param x Left boundary of the region
     * @param y Top boundary of the region
     * @param width Width of the region
     * @param height Height of the region
     * @return Pointer to DisplayRegion (nullptr if allocation failed)
     */
    DisplayRegion *createRegion(int32_t x, int32_t y, int32_t width, int32_t height)
    {
        return threadSafeDisplay.createRegion(x, y, width, height);
    }

    /**
     * Release a display region
     * @param region Pointer to the region to release
     */
    void releaseRegion(DisplayRegion *region)
    {
        threadSafeDisplay.releaseRegion(region);
    }

    /**
     * Get the underlying LGFX instance (use with caution - not thread safe)
     * This is kept for backward compatibility but should be avoided in multi-threaded code
     */
    LGFX &getGfx() { return threadSafeDisplay.getGfx(); }

    /**
     * Get the thread-safe display instance
     */
    ThreadSafeDisplay &getThreadSafeDisplay() { return threadSafeDisplay; }

    /**
     * Get display dimensions
     */
    int32_t getWidth() const { return threadSafeDisplay.getWidth(); }
    int32_t getHeight() const { return threadSafeDisplay.getHeight(); }

    /**
     * Clear the entire display (thread-safe)
     */
    void clearDisplay(const uint32_t& color = 0x0000) { threadSafeDisplay.clearDisplay(color); }

    /**
     * Set display brightness (thread-safe)
     */
    void setBrightness(uint8_t brightness) { threadSafeDisplay.setBrightness(brightness); }

private:
    void createDemoRegions()
    {
        // Create demo regions to show the concept
        auto x_middle = LCD_WIDTH / 2;
        auto y_middle = LCD_HEIGHT / 2;
        auto x_width = LCD_WIDTH / 2;
        auto y_height = LCD_HEIGHT / 2;
        auto text_offset = 10;

        Serial.printf("Display size: %dx%d\n", LCD_WIDTH, LCD_HEIGHT);
        Serial.printf("Regions: x_middle=%d, y_middle=%d, x_width=%d, y_height=%d\n",
                      x_middle, y_middle, x_width, y_height);


        //   0, 30, 85, 130

        // Create regions for each quadrant with different colors for testing
        Serial.println("Creating RED region...");
        DisplayRegion *redRegion = createRegion(0, 30, x_width, y_height - 30);
        if (redRegion)
        {
            Serial.println("RED region created successfully");
            redRegion->clear(TFT_RED);
            redRegion->setCursor(text_offset, text_offset);
            redRegion->setTextColor(TFT_WHITE);
            redRegion->setTextSize(2);
            redRegion->print("RED");
            releaseRegion(redRegion);
            Serial.println("RED region completed");
        }
        else
        {
            Serial.println("ERROR: Failed to create RED region");
        }
    }
};

#endif // DISPLAY_H