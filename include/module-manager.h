#ifndef MODULE_MANAGER_H
#define MODULE_MANAGER_H

#include <Arduino.h>
#include <vector>
#include "module.h"

class Display;

/**
 * Manages all modules in the system
 * Handles module lifecycle and event routing
 */
class ModuleManager {
public:
    ModuleManager(Display* display);
    ~ModuleManager();
    
    /**
     * Register a module with the manager
     * @param module Pointer to the module to register
     * @param x X position of the module's region
     * @param y Y position of the module's region
     * @param width Width of the module's region
     * @param height Height of the module's region
     * @return true if module was registered successfully
     */
    bool registerModule(Module* module, int32_t x, int32_t y, int32_t width, int32_t height);
    
    /**
     * Start all registered modules
     * @return true if all modules started successfully
     */
    bool startAllModules();
    
    /**
     * Stop all modules
     */
    void stopAllModules();
    
    /**
     * Send a touch event to the appropriate module(s)
     * @param type Touch event type
     * @param x X coordinate of touch
     * @param y Y coordinate of touch
     * @return true if event was sent to at least one module
     */
    bool sendTouchEvent(ModuleEventType type, int32_t x, int32_t y);
    
    /**
     * Send a button event to all modules
     * @param type Button event type
     * @param buttonId ID of the button
     * @return true if event was sent successfully
     */
    bool sendButtonEvent(ModuleEventType type, uint8_t buttonId);
    
    /**
     * Send a custom event to a specific module
     * @param moduleName Name of the target module
     * @param event The event to send
     * @return true if event was sent successfully
     */
    bool sendEventToModule(const char* moduleName, const ModuleEvent& event);
    
    /**
     * Send an event to all modules
     * @param event The event to send
     * @return Number of modules that received the event
     */
    uint32_t broadcastEvent(const ModuleEvent& event);
    
    /**
     * Get a module by name
     * @param name Name of the module to find
     * @return Pointer to the module, or nullptr if not found
     */
    Module* getModule(const char* name);
    
    /**
     * Get all registered modules
     */
    const std::vector<Module*>& getModules() const { return _modules; }
    
    /**
     * Get number of registered modules
     */
    size_t getModuleCount() const { return _modules.size(); }
    
    /**
     * Print status of all modules
     */
    void printStatus() const;

private:
    Display* _display;
    std::vector<Module*> _modules;
    std::vector<DisplayRegion*> _regions; // Track regions for cleanup

    /**
     * Find module that contains the given point
     * @param x X coordinate
     * @param y Y coordinate
     * @return Pointer to the module, or nullptr if no module contains the point
     */
    Module* findModuleAtPoint(int32_t x, int32_t y);
};

#endif // MODULE_MANAGER_H
