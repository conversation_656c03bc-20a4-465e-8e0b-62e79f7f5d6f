#include "display-region.h"
#include <stdarg.h>

DisplayRegion::DisplayRegion(LGFX& gfx, SemaphoreHandle_t mutex, int32_t x, int32_t y, int32_t width, int32_t height, uint8_t id)
    : _gfx(gfx), _mutex(mutex), _x(x), _y(y), _width(width), _height(height), _id(id)
{
    // Store original clip region (will be set when first used)
    _originalClipX = 0;
    _originalClipY = 0;
    _originalClipW = 0;
    _originalClipH = 0;
}

DisplayRegion::~DisplayRegion()
{
    // Nothing to clean up here - mutex is managed by ThreadSafeDisplay
}

bool DisplayRegion::acquireLock(TickType_t timeout)
{
    return xSemaphoreTake(_mutex, timeout) == pdTRUE;
}

void DisplayRegion::releaseLock()
{
    xSemaphoreGive(_mutex);
}

void DisplayRegion::setClipRegion()
{
    // Store the current clip region
    _gfx.getClipRect(&_originalClipX, &_originalClipY, &_originalClipW, &_originalClipH);

    // Set our region as the clip area
    _gfx.setClipRect(_x, _y, _width, _height);
}

void DisplayRegion::restoreClipRegion()
{
    // Restore the original clip region
    _gfx.setClipRect(_originalClipX, _originalClipY, _originalClipW, _originalClipH);
}

bool DisplayRegion::clipCoordinates(int32_t& x, int32_t& y, int32_t& w, int32_t& h)
{
    // Convert relative coordinates to absolute
    x += _x;
    y += _y;
    
    // Clip to region boundaries
    int32_t right = x + w;
    int32_t bottom = y + h;
    int32_t regionRight = _x + _width;
    int32_t regionBottom = _y + _height;
    
    if (x < _x) x = _x;
    if (y < _y) y = _y;
    if (right > regionRight) right = regionRight;
    if (bottom > regionBottom) bottom = regionBottom;
    
    w = right - x;
    h = bottom - y;
    
    return (w > 0 && h > 0);
}

bool DisplayRegion::contains(int32_t x, int32_t y) const
{
    return (x >= _x && x < _x + _width && y >= _y && y < _y + _height);
}

void DisplayRegion::fillRect(int32_t x, int32_t y, int32_t w, int32_t h, const uint32_t &color)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    // Convert to absolute coordinates and clip
    int32_t absX = x + _x;
    int32_t absY = y + _y;

    Serial.printf("%x, %x\n", color, TFT_RED);
    
    _gfx.fillRect(absX, absY, w, h, color);
    // _gfx.fillRect(absX, absY, w, h, TFT_RED);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::drawRect(int32_t x, int32_t y, int32_t w, int32_t h, const uint32_t &color)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    int32_t absX = x + _x;
    int32_t absY = y + _y;
    
    _gfx.drawRect(absX, absY, w, h, color);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::fillCircle(int32_t x, int32_t y, int32_t r, const uint32_t &color)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    int32_t absX = x + _x;
    int32_t absY = y + _y;
    
    _gfx.fillCircle(absX, absY, r, color);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::drawCircle(int32_t x, int32_t y, int32_t r, const uint32_t &color)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    int32_t absX = x + _x;
    int32_t absY = y + _y;
    
    _gfx.drawCircle(absX, absY, r, color);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::drawLine(int32_t x0, int32_t y0, int32_t x1, int32_t y1, const uint32_t &color)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    int32_t absX0 = x0 + _x;
    int32_t absY0 = y0 + _y;
    int32_t absX1 = x1 + _x;
    int32_t absY1 = y1 + _y;
    
    _gfx.drawLine(absX0, absY0, absX1, absY1, color);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::drawPixel(int32_t x, int32_t y, const uint32_t &color)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    int32_t absX = x + _x;
    int32_t absY = y + _y;
    
    _gfx.drawPixel(absX, absY, color);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::setCursor(int32_t x, int32_t y)
{
    if (!acquireLock()) return;
    
    int32_t absX = x + _x;
    int32_t absY = y + _y;
    
    _gfx.setCursor(absX, absY);
    
    releaseLock();
}

void DisplayRegion::setTextColor(const uint32_t &color)
{
    if (!acquireLock()) return;
    
    _gfx.setTextColor(color);
    
    releaseLock();
}

void DisplayRegion::setTextColor(const uint32_t &fgcolor, const uint32_t &bgcolor)
{
    if (!acquireLock()) return;
    
    _gfx.setTextColor(fgcolor, bgcolor);
    
    releaseLock();
}

void DisplayRegion::setTextSize(float size)
{
    if (!acquireLock()) return;
    
    _gfx.setTextSize(size);
    
    releaseLock();
}

void DisplayRegion::print(const char* text)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    _gfx.print(text);
    restoreClipRegion();
    
    releaseLock();
}

void DisplayRegion::print(const String& text)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    _gfx.print(text);
    restoreClipRegion();
    
    releaseLock();
}

void DisplayRegion::print(int value)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    _gfx.print(value);
    restoreClipRegion();
    
    releaseLock();
}

void DisplayRegion::print(float value)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    _gfx.print(value);
    restoreClipRegion();
    
    releaseLock();
}

void DisplayRegion::printf(const char* format, ...)
{
    if (!acquireLock()) return;
    
    setClipRegion();
    
    va_list args;
    va_start(args, format);
    _gfx.vprintf(format, args);
    va_end(args);
    
    restoreClipRegion();
    releaseLock();
}

void DisplayRegion::clear(const uint32_t& color)
{
    fillRect(0, 0, _width, _height, color);
}

void DisplayRegion::refresh()
{
    // For now, this is a no-op. Could be extended to force a display update
    // if the underlying display supports partial refresh
}
